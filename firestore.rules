rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Temporary: Allow all access for development and testing
    // TODO: Implement proper authentication-based rules later
    match /{document=**} {
      allow read, write: if true;
    }

    // Future secure rules (implement when adding authentication):
    /*
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }

    // Referrals collection
    match /referrals/{referralId} {
      allow read, write: if request.auth != null;
    }

    // Leaderboard collection
    match /leaderboard/{userId} {
      allow read: if true; // Public leaderboard
      allow write: if request.auth != null;
    }
    */
  }
}